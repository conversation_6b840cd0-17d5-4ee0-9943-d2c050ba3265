/* eslint-disable semi */
/* eslint-disable quotes */
"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, ActivityIndicator } from "react-native"
import { colors } from "../../theme/colors"
import { textStyles } from "../../theme/typography"
import { spacing } from "../../theme/spacing"
import Header from "../../components/common/Header"
import Input from "../../components/common/Input"
import Button from "../../components/common/Button"
import Dropdown from "../../components/common/Dropdown"
import StatusBarManager from "../../components/common/StatusBarManager"
import OTPInput from "../../components/common/OTPInput"
import { isValidEmail, isValidPhoneNumber, isValidGSTNumber, validateRequiredFields } from "../../utils/formValidation"
import alertManager from "../../utils/alertManager"
import { API_BASE_URL } from "../../config"
import { FirebaseRecaptcha } from "../../utils/recaptcha"
import { registerWithFirebase, sendPhoneVerificationCode, verifyPhoneNumber } from "../../utils/firebase"

// Registration steps - moved outside component to avoid dependency issues
const STEPS = {
  FORM: 'form',
  EMAIL_OTP: 'email_otp',
  PHONE_OTP: 'phone_otp',
}

/**
 * Registration Form component for collecting user data
 */
const RegistrationForm = ({ formData, setFormData, onSubmit, globalLoading = false }) => {
  const [isLoading, setIsLoading] = useState(false)

  // Payment terms options - memoized for performance
  const paymentTermsOptions = useMemo(() => [
    { label: "Immediate", value: "IMMEDIATE" },
    { label: "30 Days", value: "30_DAYS" },
    { label: "60 Days", value: "60_DAYS" },
  ], [])

  // Handle form input changes - memoized to prevent unnecessary re-renders
  const handleChange = useCallback((field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }, [setFormData])

  // Validate form data
  const validateForm = useCallback(() => {
    // Check if all required fields are filled
    const requiredFields = [
      "gstNumber",
      "companyName",
      "contactPerson",
      "contactNumber",
      "email",
      "password",
      "paymentTerms",
    ]

    const requiredFieldsResult = validateRequiredFields(formData, requiredFields)
    if (!requiredFieldsResult.isValid) {
      alertManager.showError("Error", requiredFieldsResult.message)
      return false
    }

    // Validate GST number format
    if (!isValidGSTNumber(formData.gstNumber)) {
      alertManager.showError("Error", "Please enter a valid GST number")
      return false
    }

    // Validate email format
    if (!isValidEmail(formData.email)) {
      alertManager.showError("Error", "Please enter a valid email address")
      return false
    }

    // Validate phone number format
    if (!isValidPhoneNumber(formData.contactNumber)) {
      alertManager.showError("Error", "Please enter a valid phone number")
      return false
    }

    return true
  }, [formData])

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      await onSubmit()
    } catch (error) {
      console.error('Form submission error:', error)
      alertManager.showError("Error", "Failed to proceed. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  // Check if any loading state is active
  const isFormDisabled = isLoading || globalLoading

  return (
    <View style={styles.container}>
      {globalLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Processing your registration...</Text>
        </View>
      )}

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
        scrollEnabled={!isFormDisabled}
      >
        <View style={styles.formContainer}>
          <Text style={styles.sectionTitle}>Company Details</Text>

          <Input
            label="GST Number"
            value={formData.gstNumber}
            onChangeText={(value) => handleChange("gstNumber", value)}
            placeholder="Enter GST number"
            autoCapitalize="characters"
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Input
            label="Company Name"
            value={formData.companyName}
            onChangeText={(value) => handleChange("companyName", value)}
            placeholder="Enter company name"
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Input
            label="Contact Person Name"
            value={formData.contactPerson}
            onChangeText={(value) => handleChange("contactPerson", value)}
            placeholder="Enter contact person name"
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Input
            label="Company Contact Number"
            value={formData.contactNumber}
            onChangeText={(value) => handleChange("contactNumber", value)}
            placeholder="Enter contact number"
            keyboardType="phone-pad"
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Input
            label="Company Contact Email"
            value={formData.email}
            onChangeText={(value) => handleChange("email", value)}
            placeholder="Enter email address"
            keyboardType="email-address"
            autoCapitalize="none"
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Input
            label="Password"
            value={formData.password}
            onChangeText={(value) => handleChange("password", value)}
            placeholder="Enter password"
            secureTextEntry
            editable={!isFormDisabled}
            style={isFormDisabled && styles.disabledInput}
          />

          <Dropdown
            label="Payment Terms"
            options={paymentTermsOptions}
            selectedValue={formData.paymentTerms}
            onValueChange={(value) => handleChange("paymentTerms", value)}
            placeholder="Select payment terms"
            disabled={isFormDisabled}
          />

          <Button
            title={isLoading ? "Processing..." : "Proceed to OTP Verification"}
            onPress={handleSubmit}
            loading={isLoading}
            disabled={isFormDisabled}
            style={styles.button}
          />
        </View>
      </ScrollView>
    </View>
  )
}

/**
 * Email OTP Verification component
 */
const EmailOTPVerification = ({ email, onVerificationSuccess, onResendRequest, globalLoading = false }) => {
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Timer for OTP resend
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [countdown, canResend]);

  // Format time for display
  const formatTime = useCallback((seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }, []);

  // Handle OTP verification
  const handleVerify = async () => {
    if (otp.length !== 6) {
      alertManager.showError('Error', 'Please enter the complete 6-digit OTP');
      return;
    }

    setIsLoading(true);
    try {
      // Verify email OTP
      const response = await fetch(`${API_BASE_URL}/auth/verify-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          otp,
        }),
      });
      const data = await response.json();
      console.log("Email verification response:", data);

      if (data.status === 'success') {
        // Email verified successfully
        alertManager.showSuccess('Success', 'Email verified successfully!');
        onVerificationSuccess();
      } else {
        throw new Error(data.message || 'Failed to verify email OTP');
      }
    } catch (error) {
      console.error('Email OTP verification error:', error);

      // Provide specific error messages
      let errorMessage = "Failed to verify email. Please try again.";
      if (error.message.includes('expired')) {
        errorMessage = "The verification code has expired. Please request a new one.";
      } else if (error.message.includes('invalid')) {
        errorMessage = "Invalid verification code. Please check and try again.";
      } else if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }

      alertManager.showError('Verification Failed', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }

  // Handle resend OTP
  const handleResend = async () => {
    if (!canResend) {
      return;
    }

    setIsLoading(true);
    try {
      await onResendRequest();
      // Reset countdown
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error('Resend OTP error:', error);
      alertManager.showError('Error', 'Failed to resend verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  // Check if any loading state is active
  const isComponentDisabled = isLoading || globalLoading

  return (
    <View style={styles.container}>
      {globalLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Verifying your email...</Text>
        </View>
      )}

      <View style={styles.otpContainer}>
        <Text style={styles.verificationTitle}>Email Verification</Text>
        <Text style={styles.verificationText}>
          Please enter the 6-digit code sent to {email}
        </Text>

        <OTPInput
          length={6}
          onCodeFilled={setOtp}
          autoFocus
          editable={!isComponentDisabled}
        />

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Resend Code in: {formatTime(countdown)}
          </Text>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!canResend || isComponentDisabled}
          >
            <Text style={[
              styles.resendButton,
              (!canResend || isComponentDisabled) && styles.disabledText,
            ]}>
              Resend Code
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title={isLoading ? "Verifying..." : "Verify Email"}
          onPress={handleVerify}
          loading={isLoading}
          disabled={isComponentDisabled || otp.length !== 6}
          style={styles.button}
        />
      </View>
    </View>
  )
}

/**
 * Phone OTP Verification component
 */
const PhoneOTPVerification = ({ phoneNumber, verificationId, onVerificationSuccess, onResendRequest, globalLoading = false }) => {
  const [otp, setOtp] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [countdown, setCountdown] = useState(60)
  const [canResend, setCanResend] = useState(false)

  // Timer for OTP resend
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
    }

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [countdown, canResend]);

  // Format time for display
  const formatTime = useCallback((seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  }, []);

  // Handle OTP verification
  const handleVerify = async () => {
    if (otp.length !== 6) {
      alertManager.showError('Error', 'Please enter the complete 6-digit OTP');
      return;
    }

    setIsLoading(true);
    try {
      // Verify phone OTP using Firebase
      const data = await verifyPhoneNumber(verificationId, otp, phoneNumber);

      if (data.status === 'success') {
        // Phone verified successfully
        alertManager.showSuccess('Success', 'Phone number verified successfully!');
        onVerificationSuccess();
      } else {
        throw new Error(data.message || 'Failed to verify phone OTP');
      }
    } catch (error) {
      console.error('Phone OTP verification error:', error);

      // Provide specific error messages
      let errorMessage = "Failed to verify phone number. Please try again.";
      if (error.message.includes('expired')) {
        errorMessage = "The verification code has expired. Please request a new one.";
      } else if (error.message.includes('invalid')) {
        errorMessage = "Invalid verification code. Please check and try again.";
      } else if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }

      alertManager.showError('Verification Failed', errorMessage);
    } finally {
      setIsLoading(false);
    }
  }

  // Handle resend OTP
  const handleResend = async () => {
    if (!canResend) {
      return;
    }

    setIsLoading(true);
    try {
      await onResendRequest();
      // Reset countdown
      setCountdown(60);
      setCanResend(false);
    } catch (error) {
      console.error('Resend OTP error:', error);
      alertManager.showError('Error', 'Failed to resend verification code. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }

  // Check if any loading state is active
  const isComponentDisabled = isLoading || globalLoading

  return (
    <View style={styles.container}>
      {globalLoading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Verifying your phone number...</Text>
        </View>
      )}

      <View style={styles.otpContainer}>
        <Text style={styles.verificationTitle}>Phone Verification</Text>
        <Text style={styles.verificationText}>
          Please enter the 6-digit code sent to {phoneNumber}
        </Text>

        <OTPInput
          length={6}
          onCodeFilled={setOtp}
          autoFocus
          editable={!isComponentDisabled}
        />

        <View style={styles.resendContainer}>
          <Text style={styles.resendText}>
            Resend Code in: {formatTime(countdown)}
          </Text>

          <TouchableOpacity
            onPress={handleResend}
            disabled={!canResend || isComponentDisabled}
          >
            <Text style={[
              styles.resendButton,
              (!canResend || isComponentDisabled) && styles.disabledText,
            ]}>
              Resend Code
            </Text>
          </TouchableOpacity>
        </View>

        <Button
          title={isLoading ? "Verifying..." : "Verify Phone"}
          onPress={handleVerify}
          loading={isLoading}
          disabled={isComponentDisabled || otp.length !== 6}
          style={styles.button}
        />
      </View>
    </View>
  )
}

/**
 * Registration screen for new users with complete OTP verification flow
 */
const RegisterScreen = ({ navigation }) => {
  // Current step in registration process
  const [currentStep, setCurrentStep] = useState(STEPS.FORM)

  // Global loading states
  const [isRegistrationLoading, setIsRegistrationLoading] = useState(false)
  const [loadingMessage, setLoadingMessage] = useState('')

  // Form data state
  const [formData, setFormData] = useState({
    gstNumber: "22AAAAA0000A1Z5",
    companyName: "Test Company Pvt Ltd",
    contactPerson: "John Doe",
    contactNumber: "9694169828",
    email: "<EMAIL>",
    password: "Test@1234",
    paymentTerms: "IMMEDIATE",
  })

  // Verification states
  const [verificationId, setVerificationId] = useState(null)

  // Render the appropriate component based on current step
  const renderCurrentStep = useCallback(() => {
    switch (currentStep) {
      case STEPS.FORM:
        return (
          <RegistrationForm
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleProceedToEmailVerification}
            globalLoading={isRegistrationLoading}
          />
        )
      case STEPS.EMAIL_OTP:
        return (
          <EmailOTPVerification
            email={formData.email}
            onVerificationSuccess={handleEmailVerificationSuccess}
            onResendRequest={handleResendEmailOTP}
            globalLoading={isRegistrationLoading}
          />
        )
      case STEPS.PHONE_OTP:
        return (
          <PhoneOTPVerification
            phoneNumber={formData.contactNumber}
            verificationId={verificationId}
            onVerificationSuccess={handlePhoneVerificationSuccess}
            onResendRequest={handleResendPhoneOTP}
            globalLoading={isRegistrationLoading}
          />
        )
      default:
        return <RegistrationForm
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleProceedToEmailVerification}
          globalLoading={isRegistrationLoading}
        />
    }
  }, [currentStep, formData, verificationId, isRegistrationLoading, handleProceedToEmailVerification, handleEmailVerificationSuccess, handlePhoneVerificationSuccess, handleResendEmailOTP, handleResendPhoneOTP])

  // Handle proceeding to email verification
  const handleProceedToEmailVerification = useCallback(async () => {
    setIsRegistrationLoading(true)
    setLoadingMessage('Sending email verification code...')

    try {
      // Request email OTP
      console.log("sending otp to email", formData.email);
      const response = await fetch(`${API_BASE_URL}/auth/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });
      const data = await response.json()
      console.log("Email OTP API response:", data);

      if (data.status === 'success') {
        // Move to email OTP verification step
        setCurrentStep(STEPS.EMAIL_OTP);
        alertManager.showSuccess('Success', 'Verification code sent to your email!');
      } else {
        throw new Error(data.message || 'Failed to send OTP')
      }
    } catch (error) {
      console.error('Email OTP request error:', error)
      let errorMessage = "Failed to send email verification code. Please try again.";
      if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }
      alertManager.showError('Error', errorMessage);
    } finally {
      setIsRegistrationLoading(false)
      setLoadingMessage('')
    }
  }, [formData.email])

  // Handle email verification success
  const handleEmailVerificationSuccess = useCallback(async () => {
    setIsRegistrationLoading(true)
    setLoadingMessage('Sending phone verification code...')

    try {
      // Request phone OTP with reCAPTCHA token
      const result = await sendPhoneVerificationCode(formData.contactNumber);

      if (result.success) {
        // Store verification ID for phone verification
        setVerificationId(result.verificationId);

        // Move to phone OTP verification step
        setCurrentStep(STEPS.PHONE_OTP);
        alertManager.showSuccess('Success', 'Verification code sent to your phone!');
      } else {
        throw new Error(result.error || 'Failed to send phone OTP');
      }
    } catch (error) {
      console.error('Phone OTP request error:', error);
      let errorMessage = "Failed to send phone verification code. Please try again.";
      if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }
      alertManager.showError('Error', errorMessage);
    } finally {
      setIsRegistrationLoading(false)
      setLoadingMessage('')
    }
  }, [formData.contactNumber])

  // Handle phone verification success
  const handlePhoneVerificationSuccess = useCallback(async () => {
    setIsRegistrationLoading(true)
    setLoadingMessage('Completing your registration...')

    try {
      // Submit complete registration data
      // register with firebase to get firebase uid and send it in request
      const firebaseResult = await registerWithFirebase(formData.email, formData.password);

      if (!firebaseResult.success) {
        throw new Error('Failed to create Firebase account');
      }

      formData.firebaseUid = firebaseResult.uid;

      console.log("form data is : ", formData)

      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      const data = await response.json()

      if (data.status === 'success') {
        // Show success message
        alertManager.showSuccess('Success', 'Registration completed successfully!');

        // Navigate to registration success screen
        navigation.navigate('RegistrationSuccess');
      } else {
        throw new Error(data.message || 'Registration failed')
      }
    } catch (error) {
      console.error('Registration error:', error)
      let errorMessage = "Failed to complete registration. Please try again.";
      if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (error.message.includes('Firebase')) {
        errorMessage = "Failed to create account. Please try again.";
      }
      alertManager.showError('Registration Failed', errorMessage);
    } finally {
      setIsRegistrationLoading(false)
      setLoadingMessage('')
    }
  }, [formData, navigation])

  // Handle resend email OTP
  const handleResendEmailOTP = useCallback(async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/send-otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: formData.email }),
      });
      const data = await response.json()

      if (data.status === 'success') {
        alertManager.showSuccess('Success', 'OTP has been resent to your email');
      } else {
        throw new Error(data.message || 'Failed to resend OTP')
      }
    } catch (error) {
      console.error('Resend email OTP error:', error)
      let errorMessage = "Failed to resend email verification code. Please try again.";
      if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }
      alertManager.showError('Error', errorMessage);
    }
  }, [formData.email])

  // Handle resend phone OTP
  const handleResendPhoneOTP = useCallback(async () => {
    try {
      // Request new phone OTP with reCAPTCHA token
      const result = await sendPhoneVerificationCode(formData.contactNumber);

      if (result.success) {
        // Update verification ID
        setVerificationId(result.verificationId);
        alertManager.showSuccess('Success', 'OTP has been resent to your phone');
      } else {
        throw new Error(result.error || 'Failed to resend phone OTP');
      }
    } catch (error) {
      console.error('Resend phone OTP error:', error);
      let errorMessage = "Failed to resend phone verification code. Please try again.";
      if (error.message.includes('network')) {
        errorMessage = "Network error. Please check your connection and try again.";
      }
      alertManager.showError('Error', errorMessage);
    }
  }, [formData.contactNumber])

  // Handle back navigation with proper step management
  const handleBackPress = useCallback(() => {
    if (currentStep === STEPS.FORM) {
      navigation.goBack();
    } else if (currentStep === STEPS.EMAIL_OTP) {
      setCurrentStep(STEPS.FORM);
    } else if (currentStep === STEPS.PHONE_OTP) {
      setCurrentStep(STEPS.EMAIL_OTP);
    }
  }, [currentStep, navigation])

  // Get appropriate header title
  const getHeaderTitle = useCallback(() => {
    switch (currentStep) {
      case STEPS.FORM:
        return "Register";
      case STEPS.EMAIL_OTP:
        return "Email Verification";
      case STEPS.PHONE_OTP:
        return "Phone Verification";
      default:
        return "Register";
    }
  }, [currentStep])

  // Render the main screen
  return (
    <View style={styles.container}>
      <StatusBarManager />
      <Header
        title={getHeaderTitle()}
        onBackPress={handleBackPress}
      />

      {isRegistrationLoading && (
        <View style={styles.globalLoadingOverlay}>
          <View style={styles.loadingModal}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingModalText}>
              {loadingMessage || 'Processing...'}
            </Text>
          </View>
        </View>
      )}

      {renderCurrentStep()}

      {/* Invisible reCAPTCHA component for Firebase Phone Auth */}
      <FirebaseRecaptcha
        onTokenReceived={(token) => {
          console.log('reCAPTCHA token received:', token);
        }}
      />
    </View>
  )
}

// Styles for all components
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
    paddingBottom: spacing.large,
  },
  formContainer: {
    padding: spacing.medium,
  },
  sectionTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.medium,
  },
  button: {
    marginTop: spacing.large,
  },
  otpContainer: {
    flex: 1,
    padding: spacing.medium,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationTitle: {
    ...textStyles.heading3,
    color: colors.textDark,
    marginBottom: spacing.small,
    textAlign: 'center',
  },
  verificationText: {
    ...textStyles.body1,
    color: colors.textMedium,
    marginBottom: spacing.large,
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: spacing.medium,
    marginBottom: spacing.large,
  },
  resendText: {
    ...textStyles.body1,
    color: colors.textMedium,
    marginRight: spacing.small,
  },
  resendButton: {
    ...textStyles.body1,
    fontWeight: 'bold',
    color: colors.primary,
  },
  disabledText: {
    color: colors.textLight,
  },
  // Loading overlay styles
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  globalLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2000,
  },
  loadingModal: {
    backgroundColor: colors.white,
    padding: spacing.large,
    borderRadius: 12,
    alignItems: 'center',
    minWidth: 200,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  loadingText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginTop: spacing.small,
    textAlign: 'center',
  },
  loadingModalText: {
    ...textStyles.body1,
    color: colors.textDark,
    marginTop: spacing.medium,
    textAlign: 'center',
    fontWeight: '500',
  },
  // Disabled input style
  disabledInput: {
    backgroundColor: colors.backgroundLight,
    color: colors.textLight,
  },
})

export default RegisterScreen
